import httpx
import logging
from typing import Optional, Dict, Any
from common.types import (
    AgentPlatformResponse,
    AgentPlatformInfo,
    A2AClientHTTPError,
    A2AClientJSONError,
)

logger = logging.getLogger(__name__)


class AgentPlatformClient:
    """智能体平台客户端"""
    
    def __init__(self, base_url: str, agent_id: str, token: str):
        """
        初始化智能体平台客户端
        
        Args:
            base_url: 智能体平台基础URL
            agent_id: 智能体ID
            token: 智能体token
        """
        self.base_url = base_url.rstrip("/")
        self.agent_id = agent_id
        self.token = token
        
    async def get_agent_base_info(self, erp: str, ext_params: Optional[Dict[str, Any]] = None) -> AgentPlatformInfo:
        """
        获取智能体基本信息
        
        Args:
            erp: 用户登录名
            ext_params: 额外参数
            
        Returns:
            AgentPlatformInfo: 智能体基本信息
            
        Raises:
            A2AClientHTTPError: HTTP请求错误
            A2AClientJSONError: JSON解析错误
        """
        url = f"{self.base_url}/api/v1/getAgentBaseInfo"
        
        headers = {
            "autobots-agent-id": self.agent_id,
            "autobots-token": self.token,
            "Content-Type": "application/json"
        }
        
        payload = {
            "erp": erp
        }
        
        if ext_params:
            payload["extParams"] = ext_params
            
        logger.info(f"正在请求智能体平台API: {url}")
        logger.debug(f"请求头: {headers}")
        logger.debug(f"请求体: {payload}")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, headers=headers, json=payload)
                
                if response.status_code != 200:
                    error_msg = f"智能体平台API请求失败: HTTP {response.status_code}"
                    logger.error(f"{error_msg}, 响应内容: {response.text}")
                    raise A2AClientHTTPError(response.status_code, error_msg)
                
                try:
                    response_data = response.json()
                    logger.debug(f"API响应: {response_data}")
                    
                    # 解析响应
                    platform_response = AgentPlatformResponse(**response_data)
                    
                    if platform_response.code != 200:
                        error_msg = f"智能体平台返回错误: code={platform_response.code}, msg={platform_response.msg}"
                        logger.error(error_msg)
                        raise A2AClientHTTPError(platform_response.code, error_msg)
                    
                    logger.info("成功获取智能体基本信息")
                    return platform_response.data
                    
                except Exception as e:
                    if isinstance(e, A2AClientHTTPError):
                        raise
                    error_msg = f"解析智能体平台响应失败: {str(e)}"
                    logger.error(error_msg)
                    raise A2AClientJSONError(error_msg) from e
                    
        except httpx.TimeoutException:
            error_msg = "智能体平台API请求超时"
            logger.error(error_msg)
            raise A2AClientHTTPError(408, error_msg)
        except httpx.RequestError as e:
            error_msg = f"智能体平台API请求错误: {str(e)}"
            logger.error(error_msg)
            raise A2AClientHTTPError(500, error_msg) from e


class AgentPlatformAdapter:
    """智能体平台适配器，将平台数据转换为AgentCard格式"""
    
    @staticmethod
    def convert_to_agent_card(
        platform_info: AgentPlatformInfo,
        url: str,
        version: str = "1.0.0"
    ) -> 'AgentCard':
        """
        将智能体平台信息转换为AgentCard
        
        Args:
            platform_info: 智能体平台信息
            url: 智能体服务URL
            version: 版本号
            
        Returns:
            AgentCard: 转换后的智能体卡片
        """
        from common.types import AgentCard, AgentSkill
        
        # 基于prompt内容生成技能列表
        skills = []
        
        # 如果有prompt，解析其中的技能信息
        if platform_info.prompt:
            # 简单的技能提取逻辑，可以根据实际需要优化
            if "智能填单" in platform_info.prompt:
                skills.append(AgentSkill(
                    id="intelligent_form_filling",
                    name="智能填单",
                    description="根据用户输入智能填写表单"
                ))
            
            if "助手" in platform_info.agentName or "助理" in platform_info.agentName:
                skills.append(AgentSkill(
                    id="general_assistant",
                    name="通用助手",
                    description="提供通用的智能助理服务"
                ))
        
        # 如果没有从prompt中提取到技能，添加默认技能
        if not skills:
            skills.append(AgentSkill(
                id="default_skill",
                name="默认技能",
                description=platform_info.introduce or platform_info.agentDesc
            ))
        
        # 基于平台信息推断能力
        capabilities = {
            "text": True,  # 默认支持文本
            "image": False,  # 可以根据实际情况调整
            "audio": False,
            "video": False,
            "streaming": True,  # 假设支持流式响应
            "pushNotifications": False,
            "stateTransitionHistory": True
        }
        
        return AgentCard(
            name=platform_info.agentName,
            description=platform_info.agentDesc or platform_info.introduce,
            version=version,
            url=url,
            capabilities=capabilities,
            skills=skills
        )
