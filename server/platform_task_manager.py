import logging
from typing import AsyncGenerator, Optional, Dict, Any
from server.task_manager import TaskManager
from client.agent_platform_client import AgentPlatformClient
from common.types import (
    GetTaskResponse, SendTaskResponse, CancelTaskResponse,
    Task, TaskStatus, TaskState, Message, MessagePart,
    AgentPlatformInfo
)

logger = logging.getLogger(__name__)


class PlatformTaskManager(TaskManager):
    """基于智能体平台的任务管理器"""
    
    def __init__(
        self,
        platform_client: AgentPlatformClient,
        platform_info: AgentPlatformInfo,
        erp: str
    ):
        """
        初始化平台任务管理器
        
        Args:
            platform_client: 智能体平台客户端
            platform_info: 智能体平台信息
            erp: 用户登录名
        """
        self.platform_client = platform_client
        self.platform_info = platform_info
        self.erp = erp
        
        # 使用平台的prompt作为系统提示
        self.system_prompt = platform_info.prompt or "您好，我是您的AI智能助理。"
        
        logger.info(f"初始化平台任务管理器: {platform_info.agentName}")
        
    async def on_get_task(self, request):
        """获取任务"""
        try:
            # 创建初始任务状态
            task = Task(
                id=request.params.id,
                sessionId=getattr(request.params, 'sessionId', 'default'),
                status=TaskStatus(state=TaskState.SUBMITTED),
                history=[Message(
                    role="system", 
                    parts=[MessagePart(
                        type="text", 
                        text=self.platform_info.introduce
                    )]
                )]
            )
            return GetTaskResponse(id=request.id, result=task)
            
        except Exception as e:
            logger.error(f"获取任务失败: {str(e)}", exc_info=True)
            raise
    
    async def on_send_task(self, request):
        """发送任务 - 这里可以集成您的智能体平台的实际处理逻辑"""
        try:
            # 获取用户消息
            user_message = ""
            if request.params.message.parts:
                user_message = request.params.message.parts[0].text or ""
            
            logger.info(f"收到任务请求: {user_message}")
            
            async def stream_response() -> AsyncGenerator[SendTaskResponse, None]:
                # 这里是一个示例实现，您可以根据实际的智能体平台API进行调整
                
                # 1. 发送工作中状态
                yield SendTaskResponse(
                    id=request.id,
                    result=Task(
                        id=request.params.id,
                        sessionId=request.params.sessionId,
                        status=TaskStatus(state=TaskState.WORKING),
                        history=[
                            request.params.message,
                            Message(
                                role="agent", 
                                parts=[MessagePart(
                                    type="text", 
                                    text="正在处理您的请求..."
                                )]
                            )
                        ]
                    )
                )
                
                # 2. 模拟处理过程（这里您可以调用实际的智能体平台API）
                response_text = await self._process_with_platform(user_message)
                
                # 3. 发送完成状态
                yield SendTaskResponse(
                    id=request.id,
                    result=Task(
                        id=request.params.id,
                        sessionId=request.params.sessionId,
                        status=TaskStatus(state=TaskState.COMPLETED),
                        history=[
                            request.params.message,
                            Message(
                                role="agent", 
                                parts=[MessagePart(
                                    type="text", 
                                    text=response_text
                                )]
                            )
                        ]
                    )
                )
                
                logger.info("任务处理完成")
            
            return stream_response()
            
        except Exception as e:
            logger.error(f"处理任务请求时发生错误: {str(e)}", exc_info=True)
            raise
    
    async def _process_with_platform(self, user_message: str) -> str:
        """
        使用智能体平台处理用户消息
        
        这里是一个示例实现，您需要根据实际的智能体平台API进行调整
        """
        try:
            # 这里可以调用您的智能体平台的实际API
            # 例如：调用对话API、工作流API等
            
            # 示例响应（基于平台信息生成）
            if "填单" in user_message or "表单" in user_message:
                return f"根据您的需求，我将帮助您进行智能填单。{self.platform_info.introduce}"
            else:
                return f"我是{self.platform_info.agentName}，{self.platform_info.introduce}。我已收到您的消息：{user_message}，正在为您处理。"
                
        except Exception as e:
            logger.error(f"平台处理失败: {str(e)}")
            return f"抱歉，处理您的请求时遇到了问题：{str(e)}"
    
    async def on_cancel_task(self, request):
        """取消任务"""
        return CancelTaskResponse(
            id=request.id, 
            result={"status": "cancelled", "message": "任务已取消"}
        )
    
    async def on_get_task_push_notification(self, request):
        """获取任务推送通知配置"""
        return GetTaskResponse(id=request.id, result={"enabled": False})
    
    async def on_set_task_push_notification(self, request):
        """设置任务推送通知配置"""
        return SendTaskResponse(id=request.id, result={"enabled": True})
    
    async def on_send_task_subscribe(self, request):
        """订阅任务"""
        async def generate_responses():
            yield SendTaskResponse(
                id=request.id, 
                result=Task(
                    id=request.params.id,
                    sessionId=request.params.sessionId,
                    status=TaskStatus(state=TaskState.WORKING),
                    history=[Message(
                        role="agent", 
                        parts=[MessagePart(
                            type="text", 
                            text=f"正在使用{self.platform_info.agentName}处理您的请求..."
                        )]
                    )]
                )
            )
            yield SendTaskResponse(
                id=request.id, 
                result=Task(
                    id=request.params.id,
                    sessionId=request.params.sessionId,
                    status=TaskStatus(state=TaskState.COMPLETED),
                    history=[Message(
                        role="agent", 
                        parts=[MessagePart(
                            type="text", 
                            text="处理完成"
                        )]
                    )]
                )
            )
        return generate_responses()
    
    async def on_resubscribe_to_task(self, request):
        """重新订阅任务"""
        async def generate_responses():
            yield SendTaskResponse(
                id=request.id, 
                result={"status": "resubscribed", "message": "任务已重新订阅"}
            )
        return generate_responses()
