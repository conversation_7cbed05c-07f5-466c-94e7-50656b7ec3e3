import os
from pathlib import Path
import json
from typing import Dict, Any, Optional

class PlatformConfig:
    """智能体平台配置管理"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path:
            self.config_path = Path(config_path)
        else:
            self.config_path = Path(__file__).parent / "config.json"
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            # 创建默认配置
            default_config = {
                "platform": {
                    "base_url": "https://your-platform-api.com",
                    "agent_id": "your-agent-id",
                    "token": "your-agent-token"
                },
                "server": {
                    "host": "0.0.0.0",
                    "port": 5002,
                    "version": "1.0.0"
                },
                "user": {
                    "erp": "your-erp-username"
                }
            }
            # 保存默认配置
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            return default_config
        
        # 读取现有配置
        with open(self.config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    
    def get_platform_config(self) -> Dict[str, str]:
        """获取智能体平台配置"""
        return self.config.get("platform", {})
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.config.get("server", {})
    
    def get_user_config(self) -> Dict[str, str]:
        """获取用户配置"""
        return self.config.get("user", {})
    
    def update_platform_config(self, **kwargs):
        """更新智能体平台配置"""
        if "platform" not in self.config:
            self.config["platform"] = {}
        
        self.config["platform"].update(kwargs)
        self._save_config()
    
    def update_user_config(self, **kwargs):
        """更新用户配置"""
        if "user" not in self.config:
            self.config["user"] = {}
        
        self.config["user"].update(kwargs)
        self._save_config()
    
    def _save_config(self):
        """保存配置到文件"""
        with open(self.config_path, "w", encoding="utf-8") as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)

# 创建全局配置实例
config = PlatformConfig()
