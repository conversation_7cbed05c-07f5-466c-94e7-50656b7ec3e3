import asyncio
import logging
from typing import Optional
from server.server import A2AServer
from server.platform_task_manager import PlatformTaskManager
from client.agent_platform_client import AgentPlatformClient, AgentPlatformAdapter
from common.types import AgentCard
from .config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


class PlatformAgent:
    """基于智能体平台的A2A智能体"""
    
    def __init__(self):
        # 获取配置
        self.platform_config = config.get_platform_config()
        self.server_config = config.get_server_config()
        self.user_config = config.get_user_config()
        
        # 验证必要的配置
        self._validate_config()
        
        # 初始化平台客户端
        self.platform_client = AgentPlatformClient(
            base_url=self.platform_config["base_url"],
            agent_id=self.platform_config["agent_id"],
            token=self.platform_config["token"]
        )
        
        self.agent_card: Optional[AgentCard] = None
        self.task_manager: Optional[PlatformTaskManager] = None
        self.server: Optional[A2AServer] = None
        
    def _validate_config(self):
        """验证配置的完整性"""
        required_platform_keys = ["base_url", "agent_id", "token"]
        for key in required_platform_keys:
            if not self.platform_config.get(key):
                raise ValueError(f"缺少必要的平台配置: {key}")
        
        if not self.user_config.get("erp"):
            raise ValueError("缺少必要的用户配置: erp")
    
    async def initialize(self):
        """初始化智能体"""
        try:
            logger.info("正在从智能体平台获取基本信息...")
            
            # 从平台获取智能体信息
            platform_info = await self.platform_client.get_agent_base_info(
                erp=self.user_config["erp"]
            )
            
            logger.info(f"成功获取智能体信息: {platform_info.agentName}")
            
            # 构建服务URL
            server_url = f"http://{self.server_config.get('host', '0.0.0.0')}:{self.server_config.get('port', 5002)}"
            
            # 转换为AgentCard
            self.agent_card = AgentPlatformAdapter.convert_to_agent_card(
                platform_info=platform_info,
                url=server_url,
                version=self.server_config.get("version", "1.0.0")
            )
            
            # 创建任务管理器
            self.task_manager = PlatformTaskManager(
                platform_client=self.platform_client,
                platform_info=platform_info,
                erp=self.user_config["erp"]
            )
            
            # 创建服务器
            self.server = A2AServer(
                host=self.server_config.get("host", "0.0.0.0"),
                port=self.server_config.get("port", 5002),
                agent_card=self.agent_card,
                task_manager=self.task_manager
            )
            
            logger.info("智能体初始化完成")
            
        except Exception as e:
            logger.error(f"智能体初始化失败: {str(e)}", exc_info=True)
            raise
    
    async def start_async(self):
        """异步启动智能体服务"""
        if not self.server:
            await self.initialize()
        
        logger.info(f"启动智能体服务: {self.agent_card.name}")
        logger.info(f"服务地址: {self.agent_card.url}")
        logger.info(f"智能体描述: {self.agent_card.description}")
        
        await self.server.start_async()
    
    def start(self):
        """同步启动智能体服务"""
        asyncio.run(self.start_async())


async def main():
    """主函数"""
    try:
        # 创建平台智能体
        agent = PlatformAgent()
        
        # 启动服务
        await agent.start_async()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    asyncio.run(main())
